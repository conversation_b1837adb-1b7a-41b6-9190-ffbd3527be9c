package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.PositionManagementQueryDTO;
import com.caidaocloud.vms.domain.base.repository.CompanyRepository;
import com.caidaocloud.vms.domain.base.repository.PostRepository;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.PositionSupplierRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectPositionRepository;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.jaxb.SpringDataJaxb;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public class ProjectPositionRepositoryImpl implements ProjectPositionRepository {

    @Autowired
    private PostRepository postRepository;
    @Autowired
    private CompanyRepository companyRepository;
    @Autowired
    private PositionSupplierRepository positionSupplierRepository;

    @Override
    @HistoryDetailRecord(entityTypes = {
            ProjectPosition.class }, historyType = HistoryType.POSITION, subType = HistoryType.BASIC_INFO, operationType = OperationType.CREATE)
    public String saveOrUpdate(ProjectPosition projectPosition) {
        if (projectPosition.getBid() == null) {
            DataInsert.identifier(ProjectPosition.identifier).insert(projectPosition);
        } else {
            DataUpdate.identifier(ProjectPosition.identifier).update(projectPosition);
        }
        return projectPosition.getBid();
    }

    @Override
    public Optional<ProjectPosition> getPosition(String positionId) {
        return Optional.ofNullable(DataQuery.identifier(ProjectPosition.identifier)
                .oneOrNull(positionId, ProjectPosition.class));
    }

    @Override
    public List<ProjectPosition> loadPositionList(String projectId) {
        return DataQuery.identifier(ProjectPosition.identifier).limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("projectId", projectId), ProjectPosition.class)
                .getItems();
    }

    @Override
    public List<ProjectPosition> loadPositionList(String projectId,String positionId) {
        return DataQuery.identifier(ProjectPosition.identifier).limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEqIf("bid", positionId, () -> StringUtils.isNotEmpty(positionId))
                        .andEq("projectId", projectId), ProjectPosition.class)
                .getItems();
    }

    @Override
    public PageResult<ProjectPosition> pagePosition(String projectId, String name, int pageSize, int pageNo) {
        MultiDataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("projectId", projectId);

        if (StringUtils.isNotEmpty(name)) {
            List<String> postIds = postRepository.regexPostIdByName(name);
            filter = filter.andIn("position", postIds);
        }
        return DataQuery.identifier(ProjectPosition.identifier).limit(pageSize, pageNo)
                .filter(filter, ProjectPosition.class);
    }

    @Override
    public void deletePosition(ProjectPosition position) {
        DataDelete.identifier(ProjectPosition.identifier).delete(position.getBid());
    }


    @Override
    public PageResult<ProjectPosition> positionManagePage(PositionManagementQueryDTO queryDTO) {
        DataFilter dataFilter = buildBaseFilter(queryDTO);
        return DataQuery.identifier(ProjectPosition.identifier).limit(queryDTO.getPageSize(), queryDTO.getPageNo())
                .filter(dataFilter, ProjectPosition.class);
    }

    /**
     * 构建基础查询条件
     */
    private DataFilter buildBaseFilter(PositionManagementQueryDTO queryDTO) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString());

        // 项目ID条件
        if (StringUtils.isNotEmpty(queryDTO.getProjectId())) {
            filter = filter.andEq("projectId", queryDTO.getProjectId());
        }

        // 供应商id
        if (StringUtils.isNotEmpty(queryDTO.getSupplierId())) {
            List<PositionSupplier> supplierList = positionSupplierRepository.listBySupplierId(queryDTO.getSupplierId());
            filter = filter.andIn("bid", Sequences.sequence(supplierList).map(PositionSupplier::getPositionId)
                    .toList());
        }

        // 审批状态条件
        if (queryDTO.getApproveStatus() != null) {
            filter = filter.andEq("status", String.valueOf(queryDTO.getApproveStatus()));
        }

        if (StringUtils.isNotEmpty(queryDTO.getCompanyId())) {
            filter = filter.andEq("companyId", queryDTO.getCompanyId());
        }

        if (StringUtils.isNotEmpty(queryDTO.getPositionName())) {
            List<String> postIds = postRepository.regexPostIdByName(queryDTO.getPositionName());
            filter = filter.andIn("position", postIds);
        }

        if (StringUtils.isNotEmpty(queryDTO.getCompanyName())) {
            List<String> companyIds = companyRepository.regexPostIdByName(queryDTO.getPositionName());
            filter = filter.andIn("company", companyIds);
        }

        return filter;
    }

}
