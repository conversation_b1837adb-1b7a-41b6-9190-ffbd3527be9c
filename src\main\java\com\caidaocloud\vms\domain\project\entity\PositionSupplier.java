package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.application.dto.PositionSupplierDto;
import com.caidaocloud.vms.domain.project.annotation.DisplayName;
import com.caidaocloud.vms.domain.project.enums.InviteStatus;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import com.caidaocloud.vms.domain.project.history.SimplifiedHistoryFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)

public class PositionSupplier extends SimplifiedHistoryFormat<PositionSupplier> {

    private String projectId;

    private String positionId;

    @DisplayName("供应商")
    private String supplierId;

    @DisplayName("供应商联系人")
    private List<String> supplierContact;


    // TODO: 2025/12/2 删除应邀状态、报价模式、报价金额 
    // TODO: 2025/12/2 新增发布状态，未发布、已发布 
    // TODO: 2025/12/2 新增供应商提供数量，所有供应商数量和和岗位计划人数做校验
    /**
     * 应邀状态：发起、同意、拒绝、终止
     */
    private InviteStatus inviteStatus;

    
    /**
     * 报价模式：按小时、按天、按月、按项目、固定价格
     */
    @DisplayName("报价模式")
    private EnumSimple quotationMode;

    /**
     * 报价金额
     */
    @DisplayName("报价金额")
    private BigDecimal quotationValue;

    public static String identifier = "entity.vms.PositionSupplier";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    /**
     * 构造函数
     */
    public PositionSupplier(String projectId, String positionId, String supplierId, List<String> supplierContact,
            QuotationMode quotationMode, BigDecimal quotationValue) {
        this.projectId = projectId;
        this.positionId = positionId;
        this.supplierId = supplierId;
        if (supplierContact != null) {
            this.supplierContact = supplierContact;
        }
        if (quotationMode != null) {
            this.quotationMode = quotationMode.toEnumSimple();
        }
        this.quotationValue = quotationValue;
    }

    /**
     * 停用供应商关系，将状态改为终止
     */
    public void terminate() {
        this.inviteStatus = InviteStatus.TERMINATED;
        update();
    }

    /**
     * 同意供应商关系
     */
    public void agree() {
        this.inviteStatus = InviteStatus.AGREED;
        update();
    }

    /**
     * 拒绝供应商关系
     */
    public void reject() {
        this.inviteStatus = InviteStatus.REJECTED;
        update();
    }

    /**
     * 设置报价信息
     */
    public void setQuotation(QuotationMode mode, BigDecimal value) {
        if (mode != null) {
            this.quotationMode = mode.toEnumSimple();
        }
        this.quotationValue = value;
        update();
    }

    @Override
    public String formatDisplay() {
        return null;
    }

    public void update(PositionSupplierDto supplierDto) {
        this.supplierId = supplierDto.getSupplierId();
        if (supplierDto.getSupplierContact() != null) {
            this.supplierContact = supplierDto.getSupplierContact();
        }
        if (supplierDto.getQuotationMode() != null) {
            this.quotationMode = supplierDto.getQuotationMode().toEnumSimple();
        }
        this.quotationValue = supplierDto.getQuotationValue();
    }

    public void publish() {
        update();
        inviteStatus = InviteStatus.INITIATED;
    }
}