package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.application.dto.PositionSupplierDto;
import com.caidaocloud.vms.domain.project.annotation.DisplayName;
import com.caidaocloud.vms.domain.project.enums.InviteStatus;
import com.caidaocloud.vms.domain.project.enums.PublishStatus;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import com.caidaocloud.vms.domain.project.history.SimplifiedHistoryFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)

public class PositionSupplier extends SimplifiedHistoryFormat<PositionSupplier> {

    private String projectId;

    private String positionId;

    @DisplayName("供应商")
    private String supplierId;

    @DisplayName("供应商联系人")
    private List<String> supplierContact;


    // TODO: 2025/12/2 新增供应商提供数量，所有供应商数量和和岗位计划人数做校验

    private PublishStatus publishStatus;

    private Integer provide;


    public static String identifier = "entity.vms.PositionSupplier";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    /**
     * 构造函数
     */
    public PositionSupplier(String projectId, String positionId, String supplierId, List<String> supplierContact, Integer provide) {
        this.projectId = projectId;
        this.positionId = positionId;
        this.supplierId = supplierId;
        if (supplierContact != null) {
            this.supplierContact = supplierContact;
        }
        this.publishStatus = PublishStatus.INIT;
        this.provide = provide;
    }

    /**
     * 停用供应商关系，将状态改为终止
     */
    public void terminate() {
        this.publishStatus = PublishStatus.INIT;
        update();
    }

    @Override
    public String formatDisplay() {
        return null;
    }

    public void update(PositionSupplierDto supplierDto) {
        this.supplierId = supplierDto.getSupplierId();
        if (supplierDto.getSupplierContact() != null) {
            this.supplierContact = supplierDto.getSupplierContact();
        }
        this.provide = supplierDto.getProvide();
    }

    public void publish() {
        update();
        publishStatus = PublishStatus.PUBLISHED;
    }
}