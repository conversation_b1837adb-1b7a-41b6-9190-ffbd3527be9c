package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.vms.domain.project.enums.DurationType;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目岗位信息")
public class ProjectPositionPageVO {

    @ApiModelProperty(value = "岗位ID")
    private String bid;

    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "项目名称")
    private String projectTxt;
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "公司信息")
    private String company;
    @ApiModelProperty(value = "公司名称")
    private String companyTxt;

    @ApiModelProperty(value = "组织信息")
    private String organization;
    @ApiModelProperty(value = "组织名称")
    private String organizationTxt;

    @ApiModelProperty(value = "岗位信息")
    private String position;
    @ApiModelProperty(value = "岗位名称")
    private String positionTxt;
    @ApiModelProperty(value = "岗位编码")
    private String positionCode;

    @ApiModelProperty("职务")
    private String jobTxt;

    @ApiModelProperty(value = "开始日期")
    private Long startDate;

    @ApiModelProperty(value = "结束日期")
    private Long endDate;

    @ApiModelProperty(value = "总预算")
    private Integer totalBudget;

    @ApiModelProperty(value = "已用预算")
    private Integer usedBudget;

    @ApiModelProperty(value = "计划人数")
    private Integer plannedHeadcount;

    @ApiModelProperty(value = "实际人数")
    private Integer actualHeadcount;

    @ApiModelProperty(value = "状态")
    private ProjectStatus approveStatus;

    @ApiModelProperty("是否紧急岗位")
    private boolean isEmergency;


    @ApiModelProperty("最小薪资范围")
    private String minSalary;
    @ApiModelProperty("最大薪资范围")
    private String maxSalary;

    @ApiModelProperty("职级关联")
    private JobGradeRange jobGrade;

}
