2025-12-01 15:02:38.511 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.vms.application.service.TmTest], using SpringBootContextLoader
2025-12-01 15:02:38.534 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.vms.application.service.TmTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-12-01 15:02:38.537 [main] INFO  o.s.t.c.s.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.caidaocloud.vms.application.service.TmTest]: TmTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-12-01 15:02:39.272 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.caidaocloud.vms.VMSApplication for test class com.caidaocloud.vms.application.service.TmTest
2025-12-01 15:02:39.490 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-12-01 15:02:39.523 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@166eab4e, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@2bbb5a90, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@f9bdf69b, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@72ed0919, org.springframework.test.context.support.DirtiesContextTestExecutionListener@2dbd8e0a, org.springframework.test.context.transaction.TransactionalTestExecutionListener@56339e6d, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@961fccc, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@db8964d7, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@296c6883, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@39729e49, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@3fd0740d, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@82eb0183]
2025-12-01 15:02:40.335 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-12-01 15:02:40.339 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-12-01 15:02:41.146 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$96290807] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-12-01 15:02:42.373 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='192.168.120.202:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-vms-service-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-12-01 15:02:43.615 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-vms-service-config, group is : CORE_HR_GROUP
2025-12-01 15:02:43.705 [main] INFO  c.c.vms.application.service.TmTest - The following profiles are active: dev
2025-12-01 15:02:47.068 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-12-01 15:02:47.373 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-12-01 15:02:47.377 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-12-01 15:02:47.462 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68ms. Found 0 repository interfaces.
2025-12-01 15:02:47.584 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-12-01 15:02:47.972 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=b4796df4-df07-3f6a-ae96-fb18eedcfdc5
2025-12-01 15:02:48.009 [main] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: Failed to introspect Class [com.caidaocloud.vms.domain.base.service.WorkflowService] from ClassLoader [sun.misc.Launcher$AppClassLoader@f0169778]
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:779)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:744)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:729)
	at org.springframework.boot.test.mock.mockito.DefinitionsParser.parse(DefinitionsParser.java:62)
	at org.springframework.boot.test.mock.mockito.MockitoPostProcessor.postProcessBeanFactory(MockitoPostProcessor.java:141)
	at org.springframework.boot.test.mock.mockito.MockitoPostProcessor.postProcessBeanFactory(MockitoPostProcessor.java:133)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:286)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:174)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:691)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:528)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: java.lang.NoClassDefFoundError: com.caidaocloud.workflow.feign.IWfRegisterFeign
	at java.lang.Class.getDeclaredFieldsImpl(Native Method)
	at java.lang.Class.getDeclaredFields(Class.java:900)
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:774)
	... 39 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.caidaocloud.workflow.feign.IWfRegisterFeign
	at java.net.URLClassLoader.findClass(URLClassLoader.java:591)
	at java.lang.ClassLoader.loadClassHelper(ClassLoader.java:954)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:899)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:352)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:882)
	... 42 common frames omitted
2025-12-01 15:02:48.011 [main] ERROR o.s.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@166eab4e] to prepare test instance [com.caidaocloud.vms.application.service.TmTest@c2de278e]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:125)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.caidaocloud.vms.domain.base.service.WorkflowService] from ClassLoader [sun.misc.Launcher$AppClassLoader@f0169778]
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:779)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:744)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:729)
	at org.springframework.boot.test.mock.mockito.DefinitionsParser.parse(DefinitionsParser.java:62)
	at org.springframework.boot.test.mock.mockito.MockitoPostProcessor.postProcessBeanFactory(MockitoPostProcessor.java:141)
	at org.springframework.boot.test.mock.mockito.MockitoPostProcessor.postProcessBeanFactory(MockitoPostProcessor.java:133)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:286)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:174)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:691)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:528)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	... 24 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com.caidaocloud.workflow.feign.IWfRegisterFeign
	at java.lang.Class.getDeclaredFieldsImpl(Native Method)
	at java.lang.Class.getDeclaredFields(Class.java:900)
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:774)
	... 39 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.caidaocloud.workflow.feign.IWfRegisterFeign
	at java.net.URLClassLoader.findClass(URLClassLoader.java:591)
	at java.lang.ClassLoader.loadClassHelper(ClassLoader.java:954)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:899)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:352)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:882)
	... 42 common frames omitted
2025-12-01 15:02:48.024 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-12-01 15:02:48.026 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
