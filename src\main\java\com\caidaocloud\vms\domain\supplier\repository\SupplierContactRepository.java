package com.caidaocloud.vms.domain.supplier.repository;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.vms.domain.supplier.entity.SupplierContact;
import com.caidaocloud.vms.domain.supplier.enums.ContactStatus;

public interface SupplierContactRepository {

	void deleteContact(String contactId);

	String saveOrUpdate(SupplierContact supplierContact);

	Optional<SupplierContact> getContact(String bid);

	List<SupplierContact> loadContactListBySupplier(String supplierId);

	List<SupplierContact> loadContactListBySuppliers(List<String> supplierIds);

	List<SupplierContact> loadContactListBySuppliers(List<String> supplierIds, ContactStatus status);

	List<SupplierContact> loadContactList(List<String> contactIds);

	/**
	 * 根据创建人ID查找供应商联系人
	 *
	 * @param createBy 创建人ID
	 * @return 供应商联系人列表
	 */
	List<SupplierContact> findByCreateBy(String createBy);

	/**
	 * 查找指定供应商的主要联系人
	 *
	 * @param supplierId 供应商ID
	 * @return 主要联系人，如果没有则返回空
	 */
	Optional<SupplierContact> findPrimaryContactBySupplier(String supplierId);
}