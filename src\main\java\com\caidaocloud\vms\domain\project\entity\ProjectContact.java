package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.ProjectContactDto;
import com.caidaocloud.vms.application.dto.base.EmpInfoDto;
import com.caidaocloud.vms.domain.project.annotation.DisplayName;
import com.caidaocloud.vms.domain.project.history.SimplifiedHistoryFormat;
import com.caidaocloud.vms.domain.supplier.enums.ContactStatus;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)

public class ProjectContact extends SimplifiedHistoryFormat<ProjectContact> {

    private String projectId;

    @DisplayName("联系人")
    private EmpSimple contact;

    @DisplayName("邮箱")
    private String email;

    @DisplayName("电话")
    private String phone;

    @DisplayName("备注")
    private String remarks;

    private EnumSimple status;

    public static String identifier = "entity.vms.ProjectContact";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    public ProjectContact(String projectId, String empId, String email, String phone) {
        if (empId == null) {
            throw new ServerException("empId cannot be null");
        }
        if (email == null) {
            throw new ServerException("Email cannot be null");
        }
        if (phone == null) {
            throw new ServerException("Phone cannot be null");
        }
        this.projectId = projectId;
        this.contact = new EmpSimple();
        this.contact.setEmpId(empId);
        this.email = email;
        this.phone = phone;
        this.status = ContactStatus.ACTIVE.toEnumSimple();
    }

    public void update(ProjectContactDto contactDto, EmpInfoDto emp) {
        this.contact = new EmpSimple();
        this.contact.setEmpId(contactDto.getEmpId());
        this.contact.setName(emp.getName());
        this.contact.setWorkno(emp.getWorkno());
        this.email = contactDto.getEmail();
        this.phone = contactDto.getPhone();
        this.remarks = contactDto.getRemarks();
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void active() {
        this.status = ContactStatus.ACTIVE.toEnumSimple();
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void inactive() {
        this.status = ContactStatus.INACTIVE.toEnumSimple();
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    @Override
    public String formatDisplay() {
        return String.format("%s,%s", contact.getWorkno(), contact.getName());
    }

}