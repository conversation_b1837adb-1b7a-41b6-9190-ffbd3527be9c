package com.caidaocloud.vms.domain.supplier.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.SupplierContactDto;
import com.caidaocloud.vms.domain.supplier.enums.ContactStatus;
import com.caidaocloud.vms.infrastructure.util.ValidationUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class SupplierContact extends DataSimple {

    private String supplierId;

    // 供应商联系人姓名（文本）
    private String contact;

    // 供应商联系人职务（文本）
    private String position;

    private String email;

    private String phone;

    private String remarks;

    // TODO: 2025/12/2 新增是否主要联系人,同一个供应商只有一个主要联系人
    private boolean isPrimary;

    private EnumSimple status;

    public static String identifier = "entity.vms.SupplierContact";

    public SupplierContact() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
    }

    public SupplierContact(String supplierId, String contact, String position, String email, String phone) {
        this();
        if (contact == null) {
            throw new ServerException("contact cannot be null");
        }
        if (email == null) {
            throw new ServerException("Email cannot be null");
        }
        if (phone == null) {
            throw new ServerException("Phone cannot be null");
        }
        this.supplierId = supplierId;
        this.contact = contact;
        this.position = position;
        this.email = email;
        this.phone = phone;

        this.status = ContactStatus.ACTIVE.toEnumSimple();
    }

    public void update(SupplierContactDto contactDto) {
        this.contact = contactDto.getContact();
        this.position = contactDto.getPosition();
        this.email = contactDto.getEmail();
        this.phone = contactDto.getPhone();
        this.remarks = contactDto.getRemarks();
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void active() {
        this.status = ContactStatus.ACTIVE.toEnumSimple();
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void inactive() {
        this.status = ContactStatus.INACTIVE.toEnumSimple();
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void checkValidate() {
        if (StringUtils.isNotEmpty(email) && !ValidationUtil.isValidEmail(email)) {
            throw new ServerException("邮箱格式错误");
        }

        if (StringUtils.isNotEmpty(phone) && !ValidationUtil.isValidChinaMobile(phone)) {
            throw new ServerException("手机号格式错误");
        }

    }
    //
    // private static SuppliersRepository repository() {
    // return SpringUtil.getBean(SuppliersRepository.class);
    // }
    //
    // public void delete() {
    // repository().deleteContact(getBid());
    // }
    //
    // public String save() {
    // setUpdateTime(System.currentTimeMillis());
    // setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    // setCreateTime(getUpdateTime());
    // setCreateBy(getUpdateBy());
    // return repository().saveContact(this);
    // }
}
