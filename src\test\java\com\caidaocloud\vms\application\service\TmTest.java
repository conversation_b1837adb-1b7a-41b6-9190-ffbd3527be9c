package com.caidaocloud.vms.application.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.application.dto.ProjectSupplierQueryDTO;
import com.caidaocloud.vms.interfaces.manager.facade.ProjectController;
import com.caidaocloud.vms.interfaces.manager.facade.ProjectSupplierController;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @date 2025/11/3
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class TmTest {

	@Autowired
	private ProjectSupplierController projectSupplierController;
	@Autowired
	private ProjectController projectController;

	@Before
	public void bf(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	@Test
	public void list(){
		ProjectSupplierQueryDTO dto = new ProjectSupplierQueryDTO();
		dto.setProjectId("2310006975248384");
		// projectSupplierController.getProjectSuppliers(dto);

	}

	@Test
	public void createDraftTest() {
		String json = "{\"projectCode\":\"011\",\"projectName\":\"测试\",\"startDate\":null,\"endDate\":null}";
		ProjectDto dto = FastjsonUtil.toObject(json, ProjectDto.class);
		dto.setProjectCode(SnowUtil.nextId());
		projectController.saveProject(dto);
	}

}
