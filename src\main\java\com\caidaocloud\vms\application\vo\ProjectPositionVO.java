package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.project.enums.DurationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目岗位信息")
public class ProjectPositionVO {

    @ApiModelProperty(value = "岗位ID")
    private String bid;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "公司信息")
    private String company;

    @ApiModelProperty(value = "组织信息")
    private String organization;

    @ApiModelProperty(value = "岗位信息")
    private String position;

    @ApiModelProperty(value = "开始日期")
    private Long startDate;

    @ApiModelProperty(value = "结束日期")
    private Long endDate;

    @ApiModelProperty(value = "持续时间类型")
    private DurationType durationType;

    @ApiModelProperty(value = "持续时间(天)")
    private Integer duration;

    @ApiModelProperty(value = "总预算")
    private Integer totalBudget;

    @ApiModelProperty(value = "已用预算")
    private Integer usedBudget;

    @ApiModelProperty(value = "计划人数")
    private Integer plannedHeadcount;

    @ApiModelProperty(value = "实际人数")
    private Integer actualHeadcount;

    @ApiModelProperty(value = "工作地点")
    private String workplace;

    @ApiModelProperty(value = "联系人信息")
    private EmpSimple contact;

    @ApiModelProperty(value = "用工类型")
    private String employmentType;

    @ApiModelProperty("是否紧急岗位")
    private boolean isEmergency;

    @ApiModelProperty("最小薪资范围")
    private String minSalary;
    @ApiModelProperty("最大薪资范围")
    private String maxSalary;

}
