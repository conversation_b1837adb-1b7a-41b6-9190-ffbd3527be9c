package com.caidaocloud.vms.infrastructure.repository;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.domain.supplier.entity.SupplierContact;
import com.caidaocloud.vms.domain.supplier.enums.ContactStatus;
import com.caidaocloud.vms.domain.supplier.repository.SupplierContactRepository;

import org.springframework.stereotype.Repository;

@Repository
public class SupplierContactRepositoryImpl implements SupplierContactRepository {

	@Override
	public void deleteContact(String contactId) {
		DataDelete.identifier(SupplierContact.identifier).delete(contactId);
	}

	@Override
	public String saveOrUpdate(SupplierContact supplierContact) {
		if (supplierContact.getBid() == null) {
			DataInsert.identifier(SupplierContact.identifier).insert(supplierContact);
		} else {
			DataUpdate.identifier(SupplierContact.identifier).update(supplierContact);
		}
		return supplierContact.getBid();
	}

	@Override
	public Optional<SupplierContact> getContact(String bid) {
		return Optional.ofNullable(DataQuery.identifier(SupplierContact.identifier)
				.oneOrNull(bid, SupplierContact.class));
	}

	@Override
	public List<SupplierContact> loadContactListBySupplier(String supplierId) {

		return DataQuery.identifier(SupplierContact.identifier).limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andEq("supplierId", supplierId), SupplierContact.class)
				.getItems();
	}

	@Override
	public List<SupplierContact> loadContactListBySuppliers(List<String> supplierIds) {
		return loadContactListBySuppliers(supplierIds, null);

	}

	@Override
	public List<SupplierContact> loadContactListBySuppliers(List<String> supplierIds, ContactStatus status) {
		DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
				.andIn("supplierId", supplierIds);
		if (status != null) {
			filter = filter.andEq("status", String.valueOf(status.getCode()));
		}
		return DataQuery.identifier(SupplierContact.identifier).limit(-1, 1)
				.filter(filter, SupplierContact.class)
				.getItems();
	}

	@Override
	public List<SupplierContact> loadContactList(List<String> contactIds) {

		return DataQuery.identifier(SupplierContact.identifier).limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andIn("bid", contactIds), SupplierContact.class)
				.getItems();
	}

	@Override
	public List<SupplierContact> findByCreateBy(String createBy) {
		return DataQuery.identifier(SupplierContact.identifier).limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andEq("createBy", createBy), SupplierContact.class)
				.getItems();
	}

	@Override
	public Optional<SupplierContact> findPrimaryContactBySupplier(String supplierId) {
		List<SupplierContact> contacts = DataQuery.identifier(SupplierContact.identifier).limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andEq("supplierId", supplierId)
						.andEq("isPrimary", Boolean.TRUE.toString()), SupplierContact.class)
				.getItems();
		return contacts.isEmpty() ? Optional.empty() : Optional.of(contacts.get(0));
	}
}